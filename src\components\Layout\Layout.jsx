import React, { useEffect } from "react";
import { useSidebar } from "../../contexts/SidebarContext";
import Sidebar from "./Sidebar";
import Header from "./Header";
import { Outlet, useNavigate } from "react-router-dom";
import { usePageHeader } from "../../hooks/usePageHeader";
import InternetConnectionMonitor from "../ErrorPages/InternetConnectionMonitor";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { getAuthToken } from "../../util/API/authStorage";

const Layout = ({ title, subtitle, children, user }) => {
  const { isCollapsed, isMobile } = useSidebar();
  const navigate = useNavigate();

  // Use the new header hook with existing props as priority
  // This preserves all existing functionality while adding auto-detection
  const headerData = usePageHeader(title, subtitle);
  console.log(user);

  useEffect(() => {
    if (!(getAuthToken() !== undefined && getAuthToken() !== null)) {
      navigate("/login");
      return;
    }
    // if (!CONSTANTS.GETMe) {
    //   api.sendRequest(CONSTANTS.API.getMe, (res) => {
    //     // console.log(res, "API");
    //     CONSTANTS.GETMe = res?.data;
    //     setUserData(res?.data);
    //     SetProfile({...res?.data});
    //   });
    // }
  }, [navigate]);

  // Calculate margin based on sidebar state
  const getMainContentMargin = () => {
    if (isMobile) {
      return "tw-ml-0"; // No margin on mobile (sidebar is drawer)
    }
    return isCollapsed ? "tw-ml-20" : "tw-ml-64"; // Collapsed: 80px, Expanded: 256px
  };
  return (
    <>
      <InternetConnectionMonitor />
      <div className=" tw-min-h-screen tw-bg-gray-50">
        <Sidebar />
        <div
          className={` tw-transition-all tw-duration-300 ${getMainContentMargin()}`}
        >
          <Header title={headerData?.title} subtitle={headerData?.subtitle} />
          <main className="">
            <Outlet />
          </main>
        </div>
      </div>
    </>
  );
};

export default Layout;
