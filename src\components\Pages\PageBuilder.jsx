import React, { useState, useEffect, useCallback, useRef } from "react";
import DragDropBuilder from "./DragDropBuilder";
import {
  Card,
  Row,
  Col,
  Button,
  Input,
  Typography,
  Space,
  Tooltip,
  Popconfirm,
  Empty,
  message,
} from "antd";
import { PlusOutlined, DeleteOutlined } from "@ant-design/icons";
import useHttp from "../../hooks/use-http";
// import useStorage from "../../hooks/use-storage"; // Using JSON storage
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { apiGenerator } from "../../util/functions";
import useDebounce from "../../hooks/useDebounce";
import ScrollPagination from "../common/ScrollPagination";
import { Edit2, FileText, Pencil, Trash2 } from "lucide-react";
import { transformGetOneToEditFormat } from "../../util/pageDataTransformer";
import SearchBar from "../common/SearchBar";
import { useNavigate } from "react-router-dom";

const { Title, Text } = Typography;
const { Search } = Input;

const PageBuilder = () => {
  // const [pages, setPages] = useState([]); // replaced by infinite scroll loader
  const [showBuilder, setShowBuilder] = useState(false);
  const [editingPage, setEditingPage] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const navigate = useNavigate();
  const api = useHttp(); // Switch to API hook
  const { isLoading } = api; // Extract loading state

  useEffect(() => {
    // API call (commented for future use)
    // api.sendRequest(CONSTANTS.API.pages.get, (res) => {
    //   console.log("Pages fetched:", res);
    //   setPages(res);
    // });
    // Initial load handled by ScrollPagination via loadPage
  }, []);

  const refreshKeyRef = useRef(0);
  const bumpRefresh = () => {
    refreshKeyRef.current += 1;
    setRefreshKey(refreshKeyRef.current);
  };

  const handleEdit = (page) => {
    // Show loading state while fetching
    navigate(`/pages/${page?.id}`);
    setEditingPage({ ...page, isLoading: true });
    setShowBuilder(true);

    api.sendRequest(
      apiGenerator(CONSTANTS.API.pages.getById, { id: page?.id }),
      (res) => {
        try {
          // console.log("Raw getOne API response:", res);

          // Transform the getOne response to edit format
          const transformedData = transformGetOneToEditFormat(res);
          // console.log("Transformed page data for editing:", transformedData);

          // Set the transformed data with loading false
          setEditingPage({
            ...transformedData,
            isLoading: false,
          });
        } catch (transformError) {
          console.error("Error transforming page data:", transformError);

          // Fallback to original logic if transformation fails
          const pageData = res?.data?.[0] || null;
          const processedPageData = {
            ...pageData,
            componentData: pageDat?.componentData || [],
            isLoading: false,
          };

          setEditingPage(processedPageData);
          message.warning("Data transformation failed, using fallback format.");
        }
      },
      null,
      null,
      (error) => {
        console.error("Error fetching page data:", error);
        message.error("Failed to load page data. Please try again.");
        setEditingPage(null);
        setShowBuilder(false);
      }
    );
  };

  const handleDelete = async (id) => {
    api.sendRequest(
      apiGenerator(CONSTANTS.API.pages.delete, { id }),
      (res) => {
        // console.log("Page deleted successfully:", res);
        setPatchedItem({ id, type: "delete" });
      },
      null,
      "Page deleted successfully!",
      (error) => {
        console.error("Error deleting page:", error);
      }
    );
  };

  const handleSave = async (updated) => {
    if (updated) {
      setPatchedItem(updated);
    } else {
      // Fallback: remount list if no item returned
      bumpRefresh();
    }
    navigate("/pages");
    setShowBuilder(false);
    setEditingPage(null);
  };

  // Debounced search and server-side loader
  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  const [totalCount, setTotalCount] = useState(0);
  const [patchedItem, setPatchedItem] = useState(null);
  const [refreshKey, setRefreshKey] = useState(0);
  const pageSize = 12;

  const loadPage = useCallback(
    ({ page, pageSize: size }) =>
      new Promise((resolve, reject) => {
        const payload = {
          page,
          limit: size,
          search: debouncedSearchTerm || "",
        };
        // console.log("Loading page:", page, "with payload:", payload);
        api.sendRequest(
          CONSTANTS.API.pages.get,
          (res) => {
            const rows = res?.data?.rows || [];
            const count = res?.data?.count || 0;
            setTotalCount(count);
            resolve({ items: rows, totalCount: count });
          },
          payload,
          null,
          (err) => reject(err)
        );
      }),
    [debouncedSearchTerm]
  );

  if (showBuilder) {
    return (
      <div className="">
        <DragDropBuilder
          page={editingPage}
          onSave={handleSave}
          onCancel={() => {
            navigate("/pages");
            setShowBuilder(false);
            setEditingPage(null);
          }}
        />
      </div>
    );
  }

  return (
    <div className="tw-p-6 tw-bg-gray-50 tw-min-h-screen">
      <div className="tw-max-w-7xl tw-mx-auto">
        {/* Header Section */}
        <div className="tw-mb-8">
          <div className="tw-flex  tw-justify-between tw-items-start tw-lg:tw-items-center tw-gap-4">
            <div>
              <Title level={2} className="!tw-mb-2">
                Pages ({totalCount})
              </Title>
            </div>

            <Button
              type="primary"
              size="large"
              icon={<PlusOutlined />}
              onClick={() => {
                navigate("add");
                setShowBuilder(true);
              }}
              className="tw-px-6 tw-h-10 tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
              // className="tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-border-0 tw-h-12 tw-px-6 tw-rounded-lg tw-font-medium hover:tw-from-blue-700 hover:tw-to-purple-700"
            >
              Create Page
            </Button>
          </div>
          {/* <Divider className="tw-my-6" /> */}
        </div>

        {/* Search and Filter Section */}
        <div className="tw-mb-6">
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={24} md={24}>
              <SearchBar type="page" handleSearch={(e) => setSearchTerm(e)} />
              {/* <Search
                placeholder="Search pages by name or slug"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                size="large"
                // className="search-input-enhanced"
                allowClear
              /> */}
            </Col>
          </Row>
        </div>

        {/* Pages Grid */}
        <ScrollPagination
          key={`pg-${debouncedSearchTerm}-${pageSize}-${refreshKey}`}
          loadPage={loadPage}
          updatedItem={patchedItem}
          idKey="id"
          renderItem={(page) => (
            <div key={page?.id} className="tw-w-full">
              <Card
                className="tw-h-full tw-shadow-sm  hover:tw-shadow-lg tw-transition-all tw-duration-300 tw-border-0 tw-rounded-2xl"
                styles={{ body: { padding: "24px" } }}
                // onClick={() => handleEdit(page)}
              >
                <div className="tw-mb-4 tw-w-full tw-flex tw-items-center tw-justify-between">
                  <span className="tw-border-[1px] tw-rounded-full tw-border-solid tw-border-[#D9DBDF] tw-px-2 tw-py-1 tw-text-xs tw-text-black">
                    v
                    {page?.pageversions?.reduce(
                      (max, v) => (v?.version > max ? v?.version : max),
                      0
                    ) || 1}
                  </span>
                  <div className="">
                    <Tooltip title="Edit Page" key="edit">
                      <Button
                        type="text"
                        icon={<Pencil width={16} height={16} />}
                        onClick={() => handleEdit(page)}
                        className="tw-p-2 tw-text-gray-400 tw-hover:tw-text-blue-600 tw-hover:tw-bg-blue-50 tw-rounded-lg tw-transition-colors"
                      />
                    </Tooltip>
                    <Tooltip title="Delete Page" key="delete">
                      <Popconfirm
                        title="Delete Page"
                        description="Are you sure you want to delete this page?"
                        onConfirm={() => handleDelete(page?.id)}
                        okText="Yes"
                        cancelText="No"
                        okButtonProps={{ danger: true }}
                      >
                        <Button
                          type="text"
                          danger
                          icon={<Trash2 className="tw-w-4 tw-h-4" />}
                          loading={isLoading}
                          disabled={isLoading}
                          className="tw-text-red-600 hover:tw-text-red-700"
                        />
                      </Popconfirm>
                    </Tooltip>
                  </div>
                </div>
                <div className="tw-flex tw-justify-between tw-items-center tw-text-xs tw-text-gray-500">
                  <Space>
                    <Title
                      level={4}
                      ellipsis={{ rows: 1, expandable: false, tooltip: true }}
                      className="!tw-mb-2 tw-truncate sm:tw-max-w-28 md:tw-max-w-40 lg:tw-max-w-auto xl:tw-max-w-auto"
                    >
                      {page.name}
                    </Title>
                  </Space>
                  <Space>
                    <span className="tw-text-base">
                      {new Date(page.createdAt).toLocaleDateString()}
                    </span>
                  </Space>
                </div>
              </Card>
            </div>
          )}
          pageSize={pageSize}
          useWindow
          className="tw-grid tw-grid-cols-1 md:tw-grid-cols-2 lg:tw-grid-cols-3 tw-gap-6"
          emptyScreen={
            <div className="tw-text-center tw-py-16">
              <Empty
                // image={Empty.PRESENTED_IMAGE_SIMPLE}
                image={
                  <FileText className="tw-w-16 tw-h-16 tw-text-gray-400" />
                }
                description={
                  <div>
                    <Title level={4} className="!tw-mb-2">
                      {debouncedSearchTerm ? "No pages found" : "No pages yet"}
                    </Title>
                    <Text type="secondary" className="tw-text-base">
                      {debouncedSearchTerm
                        ? "Try adjusting your search criteria"
                        : "Create your first page using the drag-and-drop builder"}
                    </Text>
                  </div>
                }
              ></Empty>
            </div>
          }
        />
      </div>
    </div>
  );
};

export default PageBuilder;
